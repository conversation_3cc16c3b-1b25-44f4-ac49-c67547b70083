<?php

namespace Theme\Xmetr\Http\Resources;

use Xmetr\RealEstate\Models\Property;
use Illuminate\Http\Resources\Json\JsonResource;
use Xmetr\Media\Facades\RvMedia;

/**
 * @mixin Property
 */
class PropertyResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'url' => $this->url,
            'description' => $this->description,
            'image' =>  RvMedia::getImageUrl($this->image, 'medium-square') ?? null,
            'image_thumb' => $this->image_thumb,
            'images' => $this->images,
            'price' => $this->price,
            'formatted_price' => $this->price_html,
            'location' => $this->short_address,
            'city' => $this->city->name,
            'number_bedroom' => $this->number_bedroom,
            'number_bathroom' => $this->number_bathroom,
            'square' => $this->square,
            'square_text' => $this->square_text,
            'type' => $this->type,
            'type_text' => $this->type_html,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'period' => $this->period,
            'status_html' => $this->status_html,
            'author' => [
                'id' => $this->author?->id,
                'name' => $this->author?->name,
                'phone' => $this->author?->phone,
                'whatsapp' => $this->author?->getMetaData('whatsapp', true),
                'telegram' => $this->author?->getMetaData('telegram', true),
            ],
            'category_name' => $this->category_name,
            'map_icon' => $this->map_icon,
        ];
    }
}
